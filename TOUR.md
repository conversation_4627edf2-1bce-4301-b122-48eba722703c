# PLG Journey System Implementation Plan

## Overview

This document outlines the implementation plan for a comprehensive **Product-Led Growth (PLG) Journey System** for the Rancher UI Extension. The system will provide both educational tours and guided user workflows, helping users make decisions and complete key tasks in their Rancher journey.

## System Capabilities

### 🎯 Educational Tours
- Guided walkthroughs of UI features
- Step-by-step explanations
- Interactive help system

### 🚀 PLG User Journeys
- **Decision Trees**: Guide users through choices (create credential vs import cluster vs create cluster)
- **Workflow Integration**: Help users complete actual tasks, not just learn about them
- **Progressive Onboarding**: Multi-session journeys that adapt to user progress
- **Contextual Suggestions**: Recommend next steps based on current state
- **Progress Tracking**: Show completion status and guide users toward activation

### 📊 Use Cases
- **First Login**: Present options for getting started (create credential, import cluster, create cluster)
- **Post-Cluster Creation**: Guide through user management, monitoring setup, application deployment
- **Feature Discovery**: Introduce advanced features when users are ready
- **Workflow Completion**: Track and guide users through multi-step processes

## Architecture Overview

```mermaid
graph TB
    A[User Action/Navigation] --> B[Journey Trigger System]
    B --> C[PLG Journey Store]
    C --> D{Journey State Check}
    D -->|New User| E[Launch Onboarding Journey]
    D -->|In Progress| F[Resume Journey]
    D -->|Decision Point| G[Present Choices]
    D -->|Completed| H[Suggest Next Journey]

    E --> I[Custom Journey System]
    F --> I
    G --> I
    H --> I

    I --> J[Rancher Wizard Component]
    J --> K[Journey Steps]
    K --> L[Decision Trees]
    K --> M[Workflow Integration]
    K --> N[Progress Tracking]

    C --> O[User Preferences API]
    O -->|Fallback| P[localStorage]

    Q[Journey Definitions] --> C
    R[System State] --> B
    S[User Context] --> B
    T[i18n Translations] --> I

    M --> U[Actual Rancher Actions]
    U --> V[Create Cluster/Credential/Users]
```

## Key Findings from Codebase Analysis

### ✅ Available Infrastructure
- **User Preferences System**: Robust prefs store with server-side persistence
- **Extension Architecture**: Well-defined patterns for store modules and lifecycle hooks
- **UI Components**: Rich component library (Banner, Modal, Tooltip, **Wizard**)
- **Lifecycle Hooks**: OnNavToPackage/OnNavAwayFromPackage for tour triggering
- **i18n Support**: Existing internationalization patterns
- **Wizard Component**: Perfect foundation for step-by-step tours

### ⚠️ Licensing Constraints
- **Shepherd.js**: Cannot be used due to license restrictions
- **Alternative needed**: Driver.js (MIT) or custom solution

### 💡 Recommended Approach: Custom Tour System
**Why Custom is Best**:
- ✅ No licensing issues
- ✅ Perfect integration with existing Rancher Wizard component
- ✅ Leverages existing design system (Banner, Modal, Tooltip)
- ✅ Smaller bundle size, better performance
- ✅ Full control over functionality and styling

## Implementation Tasks

### Task 1: Build Custom PLG Journey System Foundation
**Priority**: High | **Status**: Not Started

**Objectives**:
- Create custom journey system using Rancher's Wizard component as foundation
- Support both educational tours and PLG user journeys
- Implement decision trees and workflow integration
- Enable progress tracking across sessions

**Key Features**:
- **Educational Tours**: Traditional step-by-step UI walkthroughs
- **PLG Journeys**: Decision-driven workflows that help users complete tasks
- **Decision Trees**: Present choices and guide users through different paths
- **Workflow Integration**: Trigger actual Rancher actions, not just show UI
- **Progress Persistence**: Track journey state across multiple sessions

**Deliverables**:
- Base PLG journey system architecture
- Extended Wizard component for journey support
- TypeScript definitions for journey configurations
- Integration with existing Rancher components and actions

### Task 2: Implement PLG Journey Store Module
**Priority**: High | **Dependencies**: Task 1

**Objectives**:
- Create Vuex store module for managing PLG journeys and user progress
- Support complex journey definitions with branching logic
- Implement state management for decision trees and workflow states
- Track user progress across multiple sessions and journey types

**Enhanced Store Structure**:
```typescript
state: {
  journeys: {
    definitions: {},           // Journey configurations with decision trees
    userProgress: {},          // User completion status and current position
    currentJourney: null,      // Active journey instance
    journeyHistory: [],        // Completed journeys and decisions made
    systemState: {},           // Current Rancher system state (clusters, credentials, etc.)
    isActive: false,           // Journey system state
    pendingActions: [],        // Queued workflow actions
    decisionPoints: {}         // User choices at decision nodes
  }
}
```

### Task 3: Create User Preferences Integration
**Priority**: High | **Dependencies**: Task 2

**Objectives**:
- Implement tour state persistence using Rancher's user preferences API
- Create fallback to localStorage and cookies
- Follow existing prefs.js patterns

**Persistence Strategy**:
1. **Primary**: Rancher User Preferences API (server-side)
2. **Fallback**: localStorage (client-side)
3. **Last Resort**: Cookies (cross-session)

**Preference Definition**:
```javascript
export const TOUR_STATES = create('tour-states', {}, { 
  parseJSON: true,
  asUserPreference: true 
});
```

### Task 4: Build Tour Management Components
**Priority**: Medium | **Dependencies**: Task 1, 3

**Objectives**:
- Create reusable Vue components for tour overlays and navigation
- Use existing Rancher UI components as building blocks
- Ensure responsive design and accessibility

**Components to Create**:
- `TourOverlay.vue` - Main tour container
- `TourStep.vue` - Individual tour step component
- `TourNavigation.vue` - Next/Previous/Skip buttons
- `TourProgress.vue` - Progress indicator

**Reusable Rancher Components**:
- Banner - For tour notifications
- Modal/ModalManager - For tour overlays
- Tooltip/LabeledTooltip - For step highlights
- AsyncButton - For tour navigation
- Wizard - For multi-step structure

### Task 5: Implement Tour Lifecycle Hooks
**Priority**: Medium | **Dependencies**: Task 2, 4

**Objectives**:
- Integrate tour triggering with existing navigation hooks
- Create tour definitions for specific pages/features
- Implement automatic tour launching based on user state

**Integration Points**:
```typescript
const onEnter: OnNavToPackage = async(store, config) => {
  // Check if tour should be triggered for this page
  await store.dispatch('tours/checkAndLaunchTour', {
    page: config.route.name,
    user: store.getters['auth/user']
  });
};
```

### Task 6: Add Tour Configuration and Management
**Priority**: Medium | **Dependencies**: Task 5

**Objectives**:
- Create tour configuration system for defining steps and conditions
- Implement tour management features (skip, restart, disable, resume)
- Create tour definition schema and validation

**Tour Configuration Schema**:
```typescript
interface TourDefinition {
  id: string;
  name: string;
  description: string;
  steps: TourStep[];
  triggers: TourTrigger[];
  conditions?: TourCondition[];
}
```

### Task 7: Implement i18n Support
**Priority**: Low | **Dependencies**: Task 4, 6

**Objectives**:
- Add internationalization support for tour content
- Create translation keys following Rancher conventions
- Support dynamic content translation

**Translation Structure**:
```javascript
// tours.en-us.yml
tours:
  welcome:
    title: "Welcome to Rancher"
    steps:
      step1: "This is your dashboard..."
  buttons:
    next: "Next"
    skip: "Skip Tour"
    finish: "Finish"
```

### Task 8: Create Tour Testing and Documentation
**Priority**: Low | **Dependencies**: All previous tasks

**Objectives**:
- Write comprehensive unit tests for tour functionality
- Create documentation for adding new tours
- Test integration with existing Rancher Dashboard features

**Testing Areas**:
- Tour state management
- User preference persistence
- Component rendering and interaction
- Navigation hook integration
- Cross-browser compatibility

## Technical Specifications

### Tour States
- `not_started` - Tour has never been initiated
- `in_progress` - Tour is partially completed
- `completed` - Tour has been finished
- `disabled` - User has disabled this tour
- `skipped` - User has skipped this tour

### Data Persistence
**User Preferences API Structure**:
```json
{
  "tour-states": {
    "welcome-tour": "completed",
    "cluster-creation-tour": "in_progress",
    "monitoring-tour": "disabled"
  }
}
```

### Extension Integration
**Store Registration**:
```typescript
plugin.addDashboardStore('tours', toursStoreFactory(), toursConfig);
```

**Navigation Hooks**:
```typescript
plugin.addNavHooks(onEnterWithTours, onLeaveWithTours);
```

## Success Criteria

### Functional Requirements
- ✅ Tours automatically trigger on first visit to relevant pages
- ✅ User can skip, restart, or disable tours
- ✅ Tour progress persists across sessions
- ✅ Tours resume from correct step if interrupted
- ✅ Multiple tours can be defined and managed independently

### Technical Requirements
- ✅ Integrates cleanly with existing Rancher Dashboard architecture
- ✅ Uses Rancher's user preferences API for persistence
- ✅ Follows established Vuex store patterns
- ✅ Responsive and accessible UI components
- ✅ Comprehensive test coverage

### UX Requirements
- ✅ Tours don't interfere with critical Rancher UI interactions
- ✅ Consistent with Rancher's design system
- ✅ Mobile-friendly responsive design
- ✅ Supports internationalization
- ✅ Clear visual indicators and intuitive navigation

## Next Steps

1. **Start with Task 1**: Research and integrate Shepherd.js
2. **Set up development environment** for testing tour functionality
3. **Create basic tour wrapper** component with Rancher styling
4. **Test integration** with existing rancher-saas extension
5. **Iterate and refine** based on initial implementation results

## Resources

### Existing Rancher Components
- `dashboard/shell/components/Wizard.vue` - Multi-step wizard pattern
- `dashboard/shell/store/prefs.js` - User preferences system
- `pkg/rancher-saas/index.ts` - Extension registration patterns
- `dashboard/shell/components/ModalManager.vue` - Modal system
- `dashboard/pkg/rancher-components/` - UI component library

### External Dependencies
- **Driver.js** - Alternative tour library (MIT licensed) OR
- **Custom Solution** - Using existing Rancher Wizard component (Recommended)
- **Vue 3** - Framework compatibility
- **TypeScript** - Type definitions needed

## PLG Journey System Architecture

### Core Components
```typescript
// JourneyWizard.vue - Extends existing Wizard component for PLG flows
// JourneyStep.vue - Individual journey steps with decision support
// DecisionTree.vue - Choice presentation and branching logic
// WorkflowIntegration.vue - Actual Rancher action triggers
// ProgressTracker.vue - Journey progress and completion status
// JourneyManager.vue - Journey orchestration and state management
```

### PLG Journey Examples

#### First Login Journey
```typescript
const firstLoginJourney = {
  id: 'first-login',
  name: 'Getting Started with Rancher',
  trigger: { event: 'first-login', conditions: ['no-clusters', 'no-credentials'] },
  steps: [
    {
      type: 'welcome',
      content: 'Welcome to Rancher! Let\'s get you started.'
    },
    {
      type: 'decision',
      question: 'What would you like to do first?',
      choices: [
        { id: 'create-credential', label: 'Set up cloud credentials', next: 'credential-flow' },
        { id: 'import-cluster', label: 'Import existing cluster', next: 'import-flow' },
        { id: 'create-cluster', label: 'Create new cluster', next: 'create-flow' }
      ]
    }
  ]
};
```

#### Post-Cluster Creation Journey
```typescript
const postClusterJourney = {
  id: 'post-cluster-creation',
  trigger: { event: 'cluster-created', conditions: ['cluster-active'] },
  steps: [
    {
      type: 'celebration',
      content: 'Congratulations! Your cluster is ready.'
    },
    {
      type: 'decision',
      question: 'What would you like to configure next?',
      choices: [
        { id: 'setup-users', label: 'Set up user access', action: 'navigate-to-users' },
        { id: 'deploy-app', label: 'Deploy your first application', action: 'navigate-to-apps' },
        { id: 'setup-monitoring', label: 'Enable monitoring', action: 'navigate-to-monitoring' }
      ]
    }
  ]
};
```

### Workflow Integration
```vue
<template>
  <Wizard
    :steps="journeySteps"
    :banner-title="journeyTitle"
    @next="handleNext"
    @decision="handleDecision"
    @action="executeWorkflow"
  >
    <template #decision-step>
      <DecisionTree
        :choices="currentChoices"
        @choice-selected="makeDecision"
      />
    </template>

    <template #workflow-step>
      <WorkflowIntegration
        :action="currentAction"
        :context="journeyContext"
        @completed="onWorkflowComplete"
      />
    </template>
  </Wizard>
</template>
```

### Benefits of PLG Journey System
- **Drives User Activation** - Guides users to complete key workflows
- **Reduces Time to Value** - Helps users achieve success faster
- **Increases Feature Adoption** - Introduces features at the right time
- **Improves User Retention** - Provides ongoing guidance and value
- **Data-Driven Optimization** - Track journey completion and optimize flows
