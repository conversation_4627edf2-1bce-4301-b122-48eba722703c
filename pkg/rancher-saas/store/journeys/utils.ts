/**
 * Journey Store Utilities
 * 
 * Helper functions for journey management and system state tracking
 */

import type { JourneyDefinition, JourneyProgress, JourneyCondition, JourneyContext } from '../../types/journey';

/**
 * Creates a new journey progress object
 */
export function createJourneyProgress(journeyId: string, userId?: string): JourneyProgress {
  return {
    journeyId,
    userId,
    status: 'not-started',
    currentStepId: undefined,
    currentStepIndex: 0,
    completedSteps: [],
    decisions: {},
    startedAt: undefined,
    completedAt: undefined,
    lastActiveAt: new Date(),
    metadata: {},
  };
}

/**
 * Validates a journey definition
 */
export function validateJourneyDefinition(journey: JourneyDefinition): string[] {
  const errors: string[] = [];

  if (!journey.id) {
    errors.push('Journey ID is required');
  }

  if (!journey.name) {
    errors.push('Journey name is required');
  }

  if (!journey.steps || journey.steps.length === 0) {
    errors.push('Journey must have at least one step');
  }

  if (journey.steps) {
    const stepIds = new Set<string>();
    journey.steps.forEach((step, index) => {
      if (!step.id) {
        errors.push(`Step ${index} is missing an ID`);
      } else if (stepIds.has(step.id)) {
        errors.push(`Duplicate step ID: ${step.id}`);
      } else {
        stepIds.add(step.id);
      }

      if (!step.name) {
        errors.push(`Step ${step.id || index} is missing a name`);
      }

      if (!step.label) {
        errors.push(`Step ${step.id || index} is missing a label`);
      }

      if (!step.type) {
        errors.push(`Step ${step.id || index} is missing a type`);
      }

      // Validate decision step choices
      if (step.type === 'decision') {
        if (!step.choices || step.choices.length === 0) {
          errors.push(`Decision step ${step.id || index} must have choices`);
        } else {
          const choiceIds = new Set<string>();
          step.choices.forEach((choice, choiceIndex) => {
            if (!choice.id) {
              errors.push(`Choice ${choiceIndex} in step ${step.id} is missing an ID`);
            } else if (choiceIds.has(choice.id)) {
              errors.push(`Duplicate choice ID: ${choice.id} in step ${step.id}`);
            } else {
              choiceIds.add(choice.id);
            }

            if (!choice.label) {
              errors.push(`Choice ${choice.id || choiceIndex} in step ${step.id} is missing a label`);
            }
          });
        }
      }

      // Validate workflow step actions
      if (step.type === 'workflow') {
        if (!step.action) {
          errors.push(`Workflow step ${step.id || index} must have an action`);
        } else if (!step.action.type) {
          errors.push(`Workflow step ${step.id || index} action is missing a type`);
        }
      }
    });
  }

  if (!journey.triggers || journey.triggers.length === 0) {
    errors.push('Journey must have at least one trigger');
  }

  return errors;
}

/**
 * Calculates journey completion percentage
 */
export function calculateCompletionPercentage(journey: JourneyDefinition, progress: JourneyProgress): number {
  if (!journey.steps || journey.steps.length === 0) return 0;

  const visibleSteps = journey.steps.filter(step => !step.hidden);
  const totalSteps = visibleSteps.length;
  const completedSteps = progress.completedSteps.length;

  return totalSteps > 0 ? (completedSteps / totalSteps) * 100 : 0;
}

/**
 * Gets the next step in a journey
 */
export function getNextStep(journey: JourneyDefinition, currentStepId: string): any | null {
  if (!journey.steps) return null;

  const currentIndex = journey.steps.findIndex(step => step.id === currentStepId);
  if (currentIndex === -1 || currentIndex >= journey.steps.length - 1) return null;

  // Find next visible step
  for (let i = currentIndex + 1; i < journey.steps.length; i++) {
    const step = journey.steps[i];
    if (!step.hidden) {
      return step;
    }
  }

  return null;
}

/**
 * Gets the previous step in a journey
 */
export function getPreviousStep(journey: JourneyDefinition, currentStepId: string): any | null {
  if (!journey.steps) return null;

  const currentIndex = journey.steps.findIndex(step => step.id === currentStepId);
  if (currentIndex <= 0) return null;

  // Find previous visible step
  for (let i = currentIndex - 1; i >= 0; i--) {
    const step = journey.steps[i];
    if (!step.hidden) {
      return step;
    }
  }

  return null;
}

/**
 * Checks if a step is accessible based on completed steps
 */
export function isStepAccessible(journey: JourneyDefinition, stepId: string, completedSteps: string[]): boolean {
  if (!journey.steps) return false;

  const stepIndex = journey.steps.findIndex(step => step.id === stepId);
  if (stepIndex === -1) return false;

  // First step is always accessible
  if (stepIndex === 0) return true;

  // Check if all previous required steps are completed
  for (let i = 0; i < stepIndex; i++) {
    const step = journey.steps[i];
    if (!step.hidden && step.required !== false && !completedSteps.includes(step.id)) {
      return false;
    }
  }

  return true;
}

/**
 * Filters journeys based on system state and conditions
 */
export function filterAvailableJourneys(
  journeys: JourneyDefinition[], 
  systemState: Record<string, any>,
  userProgress: Record<string, JourneyProgress>,
  context?: any
): JourneyDefinition[] {
  return journeys.filter(journey => {
    // Skip completed journeys
    const progress = userProgress[journey.id];
    if (progress?.status === 'completed') return false;

    // Check trigger conditions
    return journey.triggers.some(trigger => {
      return evaluateTriggerConditions(trigger, systemState, context);
    });
  });
}

/**
 * Evaluates trigger conditions
 */
export function evaluateTriggerConditions(
  trigger: any,
  systemState: Record<string, any>,
  context?: any
): boolean {
  if (!trigger.conditions || trigger.conditions.length === 0) return true;

  return trigger.conditions.every((condition: JourneyCondition) => {
    return evaluateCondition(condition, systemState, context);
  });
}

/**
 * Evaluates a single condition
 */
export function evaluateCondition(
  condition: JourneyCondition,
  systemState: Record<string, any>,
  context?: any
): boolean {
  switch (condition.type) {
    case 'system-state':
      return evaluateSystemStateCondition(condition, systemState);
    case 'user-property':
      return evaluateUserPropertyCondition(condition, context?.user);
    case 'route':
      return evaluateRouteCondition(condition, context?.route);
    case 'custom':
      if (condition.customCheck && context) {
        return condition.customCheck(context);
      }
      return true;
    default:
      return true;
  }
}

/**
 * Evaluates system state conditions
 */
export function evaluateSystemStateCondition(
  condition: JourneyCondition,
  systemState: Record<string, any>
): boolean {
  const value = systemState[condition.property];

  switch (condition.operator) {
    case 'equals':
      return value === condition.value;
    case 'not-equals':
      return value !== condition.value;
    case 'exists':
      return value !== undefined && value !== null;
    case 'not-exists':
      return value === undefined || value === null;
    case 'contains':
      return Array.isArray(value) ? value.includes(condition.value) : 
             typeof value === 'string' ? value.includes(condition.value) : false;
    case 'greater-than':
      return typeof value === 'number' && value > condition.value;
    case 'less-than':
      return typeof value === 'number' && value < condition.value;
    default:
      return true;
  }
}

/**
 * Evaluates user property conditions
 */
export function evaluateUserPropertyCondition(
  condition: JourneyCondition,
  user: any
): boolean {
  if (!user) return false;

  const value = user[condition.property];

  switch (condition.operator) {
    case 'equals':
      return value === condition.value;
    case 'not-equals':
      return value !== condition.value;
    case 'exists':
      return value !== undefined && value !== null;
    case 'not-exists':
      return value === undefined || value === null;
    default:
      return true;
  }
}

/**
 * Evaluates route conditions
 */
export function evaluateRouteCondition(
  condition: JourneyCondition,
  route: any
): boolean {
  if (!route) return false;

  const value = route[condition.property] || route.query?.[condition.property] || route.params?.[condition.property];

  switch (condition.operator) {
    case 'equals':
      return value === condition.value;
    case 'not-equals':
      return value !== condition.value;
    case 'contains':
      return typeof value === 'string' && value.includes(condition.value);
    default:
      return true;
  }
}

/**
 * Generates a hash for system state to detect changes
 */
export function generateSystemStateHash(systemState: Record<string, any>): string {
  return JSON.stringify(systemState);
}

/**
 * Merges system state updates
 */
export function mergeSystemState(
  current: Record<string, any>,
  updates: Record<string, any>
): Record<string, any> {
  return { ...current, ...updates };
}
