/**
 * PLG Journey Store Module
 * 
 * This module manages the state for PLG journeys, user progress, decision trees,
 * and workflow states. It provides comprehensive journey management capabilities
 * with branching logic, conditional steps, and integration with Rancher functionality.
 */

import { CoreStoreSpecifics, CoreStoreConfig } from '@shell/core/types';
import state from './state';
import getters from './getters';
import mutations from './mutations';
import actions from './actions';

const journeyStoreFactory = (): CoreStoreSpecifics => {
  return {
    state,
    getters,
    mutations,
    actions
  };
};

const config: CoreStoreConfig = { 
  namespace: 'journeys',
  isClusterStore: false 
};

export default {
  specifics: journeyStoreFactory(),
  config
};

export * from './types';
export { default as journeyDefinitions } from './definitions';
