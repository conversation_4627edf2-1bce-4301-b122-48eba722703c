<template>
  <div class="progress-tracker">
    <!-- Progress Header -->
    <div class="progress-header">
      <div class="progress-info">
        <h4 class="progress-title">
          {{ journey?.name || t('journey.progress.title') }}
        </h4>
        <div class="progress-stats">
          <span class="current-step">
            {{ t('journey.progress.step', { current: currentStep, total: totalSteps }) }}
          </span>
          <span class="progress-percentage">
            {{ Math.round(progressPercentage) }}% {{ t('journey.progress.complete') }}
          </span>
        </div>
      </div>
      
      <div class="progress-actions">
        <button
          v-if="showRestartButton"
          class="btn btn-sm role-tertiary"
          @click="$emit('restart-journey')"
        >
          <i class="icon icon-refresh mr-5"></i>
          {{ t('journey.progress.restart') }}
        </button>
      </div>
    </div>

    <!-- Progress Bar -->
    <div class="progress-bar-container">
      <div class="progress-bar">
        <div 
          class="progress-fill"
          :style="{ width: `${progressPercentage}%` }"
        ></div>
      </div>
      <div class="progress-labels">
        <span class="progress-start">{{ t('journey.progress.start') }}</span>
        <span class="progress-end">{{ t('journey.progress.complete') }}</span>
      </div>
    </div>

    <!-- Step Indicators (optional detailed view) -->
    <div v-if="showStepIndicators" class="step-indicators">
      <div class="steps-container">
        <div
          v-for="(step, index) in visibleSteps"
          :key="step.id"
          class="step-indicator"
          :class="{
            'step-completed': isStepCompleted(step.id),
            'step-current': isCurrentStep(step.id),
            'step-upcoming': isUpcomingStep(step.id),
            'step-skipped': isStepSkipped(step.id)
          }"
        >
          <div class="step-circle">
            <i 
              v-if="isStepCompleted(step.id)"
              class="icon icon-checkmark"
            ></i>
            <i 
              v-else-if="isStepSkipped(step.id)"
              class="icon icon-minus"
            ></i>
            <span v-else class="step-number">{{ index + 1 }}</span>
          </div>
          
          <div class="step-info">
            <div class="step-label">{{ step.label }}</div>
            <div v-if="step.description" class="step-description">
              {{ step.description }}
            </div>
          </div>

          <!-- Connector line -->
          <div 
            v-if="index < visibleSteps.length - 1"
            class="step-connector"
            :class="{
              'connector-completed': isStepCompleted(step.id),
              'connector-active': isCurrentStep(step.id)
            }"
          ></div>
        </div>
      </div>
    </div>

    <!-- Journey Metadata -->
    <div v-if="showMetadata" class="journey-metadata">
      <div class="metadata-grid">
        <div v-if="journey?.estimatedDuration" class="metadata-item">
          <i class="icon icon-clock mr-5"></i>
          <span class="metadata-label">{{ t('journey.progress.estimatedTime') }}:</span>
          <span class="metadata-value">{{ formatDuration(journey.estimatedDuration) }}</span>
        </div>
        
        <div v-if="journey?.difficulty" class="metadata-item">
          <i class="icon icon-star mr-5"></i>
          <span class="metadata-label">{{ t('journey.progress.difficulty') }}:</span>
          <span class="metadata-value">{{ t(`journey.difficulty.${journey.difficulty}`) }}</span>
        </div>
        
        <div v-if="progress?.startedAt" class="metadata-item">
          <i class="icon icon-calendar mr-5"></i>
          <span class="metadata-label">{{ t('journey.progress.startedAt') }}:</span>
          <span class="metadata-value">{{ formatDate(progress.startedAt) }}</span>
        </div>
        
        <div v-if="timeSpent" class="metadata-item">
          <i class="icon icon-timer mr-5"></i>
          <span class="metadata-label">{{ t('journey.progress.timeSpent') }}:</span>
          <span class="metadata-value">{{ formatDuration(timeSpent) }}</span>
        </div>
      </div>
    </div>

    <!-- Journey Tags -->
    <div v-if="journey?.tags?.length && showTags" class="journey-tags">
      <span
        v-for="tag in journey.tags"
        :key="tag"
        class="journey-tag"
      >
        {{ tag }}
      </span>
    </div>

    <!-- Completion Celebration -->
    <div v-if="isCompleted" class="completion-celebration">
      <div class="celebration-content">
        <i class="icon icon-trophy icon-2x text-success"></i>
        <h4>{{ t('journey.progress.congratulations') }}</h4>
        <p>{{ t('journey.progress.journeyCompleted') }}</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';
import { mapGetters } from 'vuex';

import type { JourneyDefinition, JourneyProgress, JourneyStep } from '../../types/journey';

export default defineComponent({
  name: 'ProgressTracker',

  props: {
    currentStep: {
      type: Number,
      required: true,
    },
    totalSteps: {
      type: Number,
      required: true,
    },
    journey: {
      type: Object as () => JourneyDefinition,
      default: null,
    },
    progress: {
      type: Object as () => JourneyProgress,
      default: null,
    },
    showStepIndicators: {
      type: Boolean,
      default: false,
    },
    showMetadata: {
      type: Boolean,
      default: true,
    },
    showTags: {
      type: Boolean,
      default: false,
    },
    showRestartButton: {
      type: Boolean,
      default: false,
    },
  },

  emits: ['restart-journey'],

  computed: {
    ...mapGetters({
      t: 'i18n/t',
    }),

    progressPercentage(): number {
      if (this.totalSteps === 0) return 0;
      return (this.currentStep / this.totalSteps) * 100;
    },

    visibleSteps(): JourneyStep[] {
      return this.journey?.steps?.filter(step => !step.hidden) || [];
    },

    isCompleted(): boolean {
      return this.progress?.status === 'completed' || this.progressPercentage >= 100;
    },

    timeSpent(): number | null {
      if (!this.progress?.startedAt) return null;
      
      const endTime = this.progress.completedAt || new Date();
      const startTime = new Date(this.progress.startedAt);
      
      return Math.floor((endTime.getTime() - startTime.getTime()) / (1000 * 60)); // minutes
    },
  },

  methods: {
    isStepCompleted(stepId: string): boolean {
      return this.progress?.completedSteps?.includes(stepId) || false;
    },

    isCurrentStep(stepId: string): boolean {
      return this.progress?.currentStepId === stepId;
    },

    isUpcomingStep(stepId: string): boolean {
      return !this.isStepCompleted(stepId) && !this.isCurrentStep(stepId);
    },

    isStepSkipped(stepId: string): boolean {
      // TODO: Implement step skipping logic
      return false;
    },

    formatDuration(minutes: number): string {
      if (minutes < 60) {
        return this.t('journey.progress.duration.minutes', { count: minutes });
      }
      
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      
      if (remainingMinutes === 0) {
        return this.t('journey.progress.duration.hours', { count: hours });
      }
      
      return this.t('journey.progress.duration.hoursMinutes', { 
        hours, 
        minutes: remainingMinutes 
      });
    },

    formatDate(date: Date | string): string {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      return dateObj.toLocaleDateString(undefined, {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    },
  },
});
</script>

<style lang="scss" scoped>
.progress-tracker {
  .progress-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;

    .progress-info {
      flex: 1;

      .progress-title {
        margin: 0 0 8px 0;
        color: var(--primary-text);
        font-size: 1.2rem;
        font-weight: 600;
      }

      .progress-stats {
        display: flex;
        gap: 15px;
        font-size: 0.9rem;
        color: var(--body-text);

        .current-step {
          font-weight: 500;
        }

        .progress-percentage {
          color: var(--primary);
          font-weight: 600;
        }
      }
    }

    .progress-actions {
      flex-shrink: 0;
    }
  }

  .progress-bar-container {
    margin-bottom: 20px;

    .progress-bar {
      width: 100%;
      height: 12px;
      background: var(--border);
      border-radius: 6px;
      overflow: hidden;
      margin-bottom: 8px;

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, var(--primary), var(--success));
        border-radius: 6px;
        transition: width 0.5s ease;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          width: 20px;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3));
          animation: shimmer 2s infinite;
        }
      }
    }

    .progress-labels {
      display: flex;
      justify-content: space-between;
      font-size: 0.8rem;
      color: var(--muted);
    }
  }

  .step-indicators {
    margin-bottom: 20px;

    .steps-container {
      position: relative;

      .step-indicator {
        display: flex;
        align-items: flex-start;
        gap: 15px;
        margin-bottom: 20px;
        position: relative;

        .step-circle {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 0.9rem;
          font-weight: 600;
          flex-shrink: 0;
          border: 2px solid var(--border);
          background: var(--body-bg);
          color: var(--muted);
          transition: all 0.3s ease;

          .step-number {
            font-size: 0.85rem;
          }
        }

        .step-info {
          flex: 1;
          padding-top: 4px;

          .step-label {
            font-weight: 500;
            color: var(--body-text);
            margin-bottom: 4px;
          }

          .step-description {
            font-size: 0.85rem;
            color: var(--muted);
            line-height: 1.4;
          }
        }

        .step-connector {
          position: absolute;
          left: 15px;
          top: 32px;
          width: 2px;
          height: 20px;
          background: var(--border);
          transition: background 0.3s ease;
        }

        &.step-completed {
          .step-circle {
            background: var(--success);
            border-color: var(--success);
            color: white;
          }

          .step-info .step-label {
            color: var(--primary-text);
          }

          .step-connector {
            background: var(--success);
          }
        }

        &.step-current {
          .step-circle {
            background: var(--primary);
            border-color: var(--primary);
            color: white;
            box-shadow: 0 0 0 4px rgba(var(--primary-rgb), 0.2);
          }

          .step-info .step-label {
            color: var(--primary-text);
            font-weight: 600;
          }

          .step-connector {
            background: var(--primary);
          }
        }

        &.step-skipped {
          .step-circle {
            background: var(--warning);
            border-color: var(--warning);
            color: white;
          }

          .step-info .step-label {
            color: var(--muted);
            text-decoration: line-through;
          }
        }

        &:last-child .step-connector {
          display: none;
        }
      }
    }
  }

  .journey-metadata {
    margin-bottom: 20px;
    padding: 15px;
    background: var(--box-bg);
    border-radius: 6px;
    border: 1px solid var(--border);

    .metadata-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;

      .metadata-item {
        display: flex;
        align-items: center;
        gap: 5px;
        font-size: 0.9rem;

        .metadata-label {
          color: var(--body-text);
          font-weight: 500;
        }

        .metadata-value {
          color: var(--primary-text);
          font-weight: 600;
        }

        .icon {
          color: var(--primary);
        }
      }
    }
  }

  .journey-tags {
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .journey-tag {
      padding: 4px 8px;
      background: var(--primary-bg);
      color: var(--primary);
      border-radius: 12px;
      font-size: 0.8rem;
      font-weight: 500;
    }
  }

  .completion-celebration {
    text-align: center;
    padding: 30px;
    background: var(--success-bg);
    border-radius: 8px;
    border: 1px solid var(--success);

    .celebration-content {
      h4 {
        margin: 15px 0 10px 0;
        color: var(--success);
        font-size: 1.3rem;
      }

      p {
        margin: 0;
        color: var(--body-text);
        font-size: 1.1rem;
      }
    }
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-20px);
  }
  100% {
    transform: translateX(100px);
  }
}

// Responsive design
@media (max-width: 768px) {
  .progress-tracker {
    .progress-header {
      flex-direction: column;
      gap: 15px;

      .progress-stats {
        flex-direction: column;
        gap: 5px;
      }
    }

    .journey-metadata {
      .metadata-grid {
        grid-template-columns: 1fr;
        gap: 10px;
      }
    }

    .step-indicators {
      .steps-container {
        .step-indicator {
          .step-info {
            .step-description {
              display: none;
            }
          }
        }
      }
    }
  }
}
</style>
