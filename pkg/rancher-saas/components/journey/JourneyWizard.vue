<template>
  <div class="journey-wizard">
    <!-- Journey Loading State -->
    <div v-if="isLoading" class="journey-loading">
      <Loading mode="relative" />
    </div>

    <!-- Journey Content -->
    <div v-else-if="currentJourney" class="journey-content">
      <Wizard
        ref="wizard"
        :steps="wizardSteps"
        :banner-title="currentJourney.bannerTitle"
        :banner-title-subtext="currentJourney.bannerTitleSubtext"
        :banner-image="currentJourney.bannerImage"
        :show-steps="currentJourney.showProgress !== false"
        :errors="errors"
        header-mode="journey"
        finish-mode="complete"
        @next="handleNext"
        @cancel="handleCancel"
        @finish="handleFinish"
        @error="handleError"
      >
        <!-- Custom step container for journey-specific content -->
        <template #stepContainer="{ activeStep }">
          <div class="journey-step-container">
            <!-- Welcome Step -->
            <div v-if="activeStep.name === currentStep?.id && currentStep?.type === 'welcome'" class="journey-step welcome-step">
              <div class="welcome-content">
                <h3>{{ currentStep.label }}</h3>
                <p v-if="currentStep.description">{{ currentStep.description }}</p>
                <div v-if="currentStep.content" v-html="currentStep.content"></div>
              </div>
            </div>

            <!-- Education Step -->
            <div v-else-if="activeStep.name === currentStep?.id && currentStep?.type === 'education'" class="journey-step education-step">
              <div class="education-content">
                <h3>{{ currentStep.label }}</h3>
                <p v-if="currentStep.description">{{ currentStep.description }}</p>
                <div v-if="currentStep.content" v-html="currentStep.content"></div>
              </div>
            </div>

            <!-- Decision Step -->
            <div v-else-if="activeStep.name === currentStep?.id && currentStep?.type === 'decision'" class="journey-step decision-step">
              <DecisionTree
                :step="currentStep"
                :choices="currentStep.choices || []"
                :selected-choice="selectedDecision"
                @choice-selected="handleDecisionMade"
              />
            </div>

            <!-- Workflow Step -->
            <div v-else-if="activeStep.name === currentStep?.id && currentStep?.type === 'workflow'" class="journey-step workflow-step">
              <WorkflowIntegration
                :step="currentStep"
                :action="currentStep.action"
                :context="journeyContext"
                @workflow-started="handleWorkflowStarted"
                @workflow-completed="handleWorkflowCompleted"
                @workflow-error="handleWorkflowError"
              />
            </div>

            <!-- Celebration Step -->
            <div v-else-if="activeStep.name === currentStep?.id && currentStep?.type === 'celebration'" class="journey-step celebration-step">
              <div class="celebration-content">
                <div class="celebration-icon">
                  <i class="icon icon-checkmark icon-3x text-success"></i>
                </div>
                <h3>{{ currentStep.label }}</h3>
                <p v-if="currentStep.description">{{ currentStep.description }}</p>
                <div v-if="currentStep.content" v-html="currentStep.content"></div>
              </div>
            </div>

            <!-- Custom Step -->
            <div v-else-if="activeStep.name === currentStep?.id && currentStep?.type === 'custom'" class="journey-step custom-step">
              <component
                :is="currentStep.component"
                v-if="currentStep.component"
                :step="currentStep"
                :context="journeyContext"
                @step-completed="handleStepCompleted"
                @step-error="handleStepError"
              />
              <div v-else class="custom-content">
                <h3>{{ currentStep.label }}</h3>
                <p v-if="currentStep.description">{{ currentStep.description }}</p>
                <div v-if="currentStep.content" v-html="currentStep.content"></div>
              </div>
            </div>
          </div>
        </template>

        <!-- Custom controls for journey navigation -->
        <template #controlsContainer="{ showPrevious, next, back, canNext, activeStepIndex, visibleSteps, finish, cancel }">
          <div class="journey-controls">
            <!-- Error messages -->
            <div v-for="(err, idx) in errorStrings" :key="idx">
              <Banner
                color="error"
                :label="err"
                :closable="true"
                class="footer-error"
                @close="errors.splice(idx, 1)"
              />
            </div>

            <!-- Progress indicator -->
            <ProgressTracker
              v-if="currentJourney?.showProgress !== false"
              :current-step="activeStepIndex + 1"
              :total-steps="visibleSteps.length"
              :journey="currentJourney"
              :progress="currentProgress"
              class="mb-20"
            />

            <!-- Navigation buttons -->
            <div class="journey-navigation">
              <div class="journey-nav-left">
                <button
                  v-if="showPrevious && !isFirstStep"
                  class="btn role-secondary"
                  :disabled="!canGoPrevious"
                  @click="back"
                >
                  {{ t('journey.buttons.previous') }}
                </button>
                
                <button
                  v-if="currentJourney?.allowSkip !== false"
                  class="btn role-tertiary ml-10"
                  @click="handleSkip"
                >
                  {{ t('journey.buttons.skip') }}
                </button>
              </div>

              <div class="journey-nav-right">
                <button
                  v-if="!isLastStep"
                  class="btn role-primary"
                  :disabled="!canNext || !canGoNext"
                  @click="next"
                >
                  {{ getNextButtonLabel() }}
                </button>

                <AsyncButton
                  v-else
                  mode="finish"
                  class="btn role-primary"
                  :disabled="!canNext"
                  @click="finish"
                >
                  {{ t('journey.buttons.finish') }}
                </AsyncButton>
              </div>
            </div>
          </div>
        </template>
      </Wizard>
    </div>

    <!-- No Journey State -->
    <div v-else class="no-journey">
      <Banner color="info" :label="t('journey.messages.noActiveJourney')" />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, ref, watch } from 'vue';
import { mapGetters, mapActions } from 'vuex';
import Wizard from '@shell/components/Wizard.vue';
import { Banner } from '@components/Banner';
import Loading from '@shell/components/Loading.vue';
import AsyncButton from '@shell/components/AsyncButton.vue';

import DecisionTree from './DecisionTree.vue';
import WorkflowIntegration from './WorkflowIntegration.vue';
import ProgressTracker from './ProgressTracker.vue';

import type { JourneyDefinition, JourneyStep, JourneyProgress, JourneyContext, JourneyChoice } from '../../types/journey';

export default defineComponent({
  name: 'JourneyWizard',

  components: {
    Wizard,
    Banner,
    Loading,
    AsyncButton,
    DecisionTree,
    WorkflowIntegration,
    ProgressTracker,
  },

  props: {
    journeyId: {
      type: String,
      default: null,
    },
  },

  emits: [
    'journey-started',
    'journey-completed',
    'journey-skipped',
    'journey-error',
    'step-completed',
    'decision-made',
    'workflow-completed',
  ],

  setup() {
    const errors = ref<string[]>([]);
    const selectedDecision = ref<string | null>(null);

    return {
      errors,
      selectedDecision,
    };
  },

  computed: {
    ...mapGetters({
      t: 'i18n/t',
    }),

    ...mapGetters('journeys', [
      'currentJourney',
      'currentStep',
      'currentProgress',
      'getCurrentStepIndex',
      'getTotalSteps',
      'getVisibleSteps',
      'canGoNext',
      'canGoPrevious',
      'isJourneysEnabled',
    ]),

    isLoading(): boolean {
      return this.$store.getters['journeys/isLoading'];
    },

    journeyContext(): JourneyContext {
      return {
        journey: this.currentJourney,
        progress: this.currentProgress,
        user: this.$store.getters['auth/user'],
        route: this.$route,
        store: this.$store,
        systemState: this.$store.getters['journeys/getSystemState'],
      };
    },

    wizardSteps(): any[] {
      if (!this.currentJourney?.steps) return [];

      return this.currentJourney.steps
        .filter(step => !step.hidden && this.evaluateCondition(step.condition))
        .map(step => ({
          name: step.id,
          label: step.label,
          subtext: step.subtext || step.description,
          ready: step.ready !== false,
          loading: step.loading || false,
          hidden: step.hidden || false,
          nextButton: step.nextButton,
          previousButton: step.previousButton,
        }));
    },

    isFirstStep(): boolean {
      return this.getCurrentStepIndex === 0;
    },

    isLastStep(): boolean {
      return this.getCurrentStepIndex === this.getTotalSteps - 1;
    },

    errorStrings(): string[] {
      return this.errors.map(err => typeof err === 'string' ? err : err.message || 'Unknown error');
    },
  },

  watch: {
    journeyId: {
      immediate: true,
      handler(newJourneyId: string) {
        if (newJourneyId && newJourneyId !== this.currentJourney?.id) {
          this.loadJourney(newJourneyId);
        }
      },
    },
  },

  methods: {
    ...mapActions('journeys', [
      'startJourney',
      'resumeJourney',
      'completeJourney',
      'skipJourney',
      'nextStep',
      'previousStep',
      'makeDecision',
      'executeAction',
    ]),

    async loadJourney(journeyId: string) {
      try {
        await this.startJourney(journeyId);
        this.$emit('journey-started', { journeyId, journey: this.currentJourney });
      } catch (error) {
        this.handleError(error);
      }
    },

    async handleNext() {
      try {
        await this.nextStep();
      } catch (error) {
        this.handleError(error);
      }
    },

    async handleCancel() {
      await this.handleSkip();
    },

    async handleFinish() {
      try {
        await this.completeJourney(this.currentJourney?.id);
        this.$emit('journey-completed', { 
          journeyId: this.currentJourney?.id, 
          journey: this.currentJourney,
          progress: this.currentProgress 
        });
      } catch (error) {
        this.handleError(error);
      }
    },

    async handleSkip() {
      try {
        await this.skipJourney(this.currentJourney?.id);
        this.$emit('journey-skipped', { 
          journeyId: this.currentJourney?.id, 
          journey: this.currentJourney 
        });
      } catch (error) {
        this.handleError(error);
      }
    },

    async handleDecisionMade(choice: JourneyChoice) {
      try {
        this.selectedDecision = choice.id;
        await this.makeDecision(this.currentStep?.id, choice.id, choice);
        this.$emit('decision-made', { 
          stepId: this.currentStep?.id, 
          choice,
          journey: this.currentJourney 
        });
      } catch (error) {
        this.handleError(error);
      }
    },

    handleWorkflowStarted(data: any) {
      // Handle workflow start
    },

    handleWorkflowCompleted(data: any) {
      this.$emit('workflow-completed', data);
    },

    handleWorkflowError(error: Error) {
      this.handleError(error);
    },

    handleStepCompleted(data: any) {
      this.$emit('step-completed', data);
    },

    handleStepError(error: Error) {
      this.handleError(error);
    },

    handleError(error: any) {
      console.error('Journey error:', error);
      const errorMessage = typeof error === 'string' ? error : error?.message || 'An error occurred';
      this.errors.push(errorMessage);
      this.$emit('journey-error', { error, journey: this.currentJourney });
    },

    evaluateCondition(condition: any): boolean {
      if (!condition) return true;
      // TODO: Implement condition evaluation logic
      return true;
    },

    getNextButtonLabel(): string {
      const step = this.currentStep;
      if (step?.nextButton?.labelKey) {
        return this.t(step.nextButton.labelKey);
      }
      if (step?.type === 'decision') {
        return this.t('journey.buttons.continue');
      }
      return this.t('journey.buttons.next');
    },
  },
});
</script>

<style lang="scss" scoped>
.journey-wizard {
  .journey-content {
    .journey-step-container {
      min-height: 300px;
      padding: 20px;

      .journey-step {
        &.welcome-step,
        &.education-step {
          .welcome-content,
          .education-content {
            text-align: center;
            max-width: 600px;
            margin: 0 auto;

            h3 {
              margin-bottom: 15px;
              color: var(--primary-text);
            }

            p {
              margin-bottom: 20px;
              color: var(--body-text);
              line-height: 1.6;
            }
          }
        }

        &.celebration-step {
          .celebration-content {
            text-align: center;
            max-width: 600px;
            margin: 0 auto;

            .celebration-icon {
              margin-bottom: 20px;
            }

            h3 {
              margin-bottom: 15px;
              color: var(--success);
            }
          }
        }
      }
    }

    .journey-controls {
      .journey-navigation {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20px;

        .journey-nav-left,
        .journey-nav-right {
          display: flex;
          align-items: center;
        }
      }
    }
  }

  .no-journey {
    padding: 40px;
    text-align: center;
  }

  .journey-loading {
    min-height: 200px;
    position: relative;
  }
}
</style>
