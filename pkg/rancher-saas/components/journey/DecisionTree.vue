<template>
  <div class="decision-tree">
    <div class="decision-header">
      <h3 v-if="step.label" class="decision-title">
        {{ step.label }}
      </h3>
      <p v-if="step.question" class="decision-question">
        {{ step.question }}
      </p>
      <p v-else-if="step.description" class="decision-description">
        {{ step.description }}
      </p>
    </div>

    <div class="decision-choices">
      <div
        v-for="choice in choices"
        :key="choice.id"
        class="choice-card"
        :class="{
          'choice-selected': selectedChoice === choice.id,
          'choice-disabled': !isChoiceAvailable(choice)
        }"
        @click="selectChoice(choice)"
      >
        <div class="choice-content">
          <!-- Choice Icon -->
          <div v-if="choice.icon" class="choice-icon">
            <i :class="choice.icon" class="icon icon-2x"></i>
          </div>

          <!-- Choice Text -->
          <div class="choice-text">
            <h4 class="choice-label">{{ choice.label }}</h4>
            <p v-if="choice.description" class="choice-description">
              {{ choice.description }}
            </p>
          </div>

          <!-- Selection Indicator -->
          <div class="choice-indicator">
            <i 
              v-if="selectedChoice === choice.id"
              class="icon icon-checkmark text-success"
            ></i>
            <i 
              v-else-if="!isChoiceAvailable(choice)"
              class="icon icon-lock text-muted"
            ></i>
            <i 
              v-else
              class="icon icon-chevron-right text-muted"
            ></i>
          </div>
        </div>

        <!-- Choice Details (expandable) -->
        <div 
          v-if="choice.details && selectedChoice === choice.id"
          class="choice-details"
        >
          <div class="choice-details-content">
            {{ choice.details }}
          </div>
        </div>
      </div>
    </div>

    <!-- Additional Information -->
    <div v-if="step.content" class="decision-content">
      <div v-html="step.content"></div>
    </div>

    <!-- Help Text -->
    <div v-if="showHelp" class="decision-help">
      <Banner color="info" icon="icon-info">
        <template #default>
          <div class="help-content">
            <h5>{{ t('journey.decision.helpTitle') }}</h5>
            <p>{{ t('journey.decision.helpText') }}</p>
            <ul v-if="helpItems.length">
              <li v-for="item in helpItems" :key="item">{{ item }}</li>
            </ul>
          </div>
        </template>
      </Banner>
    </div>

    <!-- Action Buttons -->
    <div class="decision-actions">
      <button
        v-if="allowHelp"
        class="btn role-tertiary"
        @click="toggleHelp"
      >
        <i class="icon icon-help mr-5"></i>
        {{ showHelp ? t('journey.decision.hideHelp') : t('journey.decision.showHelp') }}
      </button>

      <button
        v-if="allowSkip"
        class="btn role-secondary ml-10"
        @click="skipDecision"
      >
        {{ t('journey.decision.skip') }}
      </button>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue';
import { mapGetters } from 'vuex';
import { Banner } from '@components/Banner';

import type { JourneyStep, JourneyChoice, JourneyCondition } from '../../types/journey';

export default defineComponent({
  name: 'DecisionTree',

  components: {
    Banner,
  },

  props: {
    step: {
      type: Object as () => JourneyStep,
      required: true,
    },
    choices: {
      type: Array as () => JourneyChoice[],
      default: () => [],
    },
    selectedChoice: {
      type: String,
      default: null,
    },
    allowHelp: {
      type: Boolean,
      default: true,
    },
    allowSkip: {
      type: Boolean,
      default: false,
    },
  },

  emits: ['choice-selected', 'choice-skipped'],

  setup() {
    const showHelp = ref(false);

    return {
      showHelp,
    };
  },

  computed: {
    ...mapGetters({
      t: 'i18n/t',
    }),

    helpItems(): string[] {
      // Generate contextual help items based on choices
      return this.choices.map(choice => 
        this.t(`journey.decision.help.${choice.id}`, {}, false) || choice.description || choice.label
      ).filter(Boolean);
    },
  },

  methods: {
    selectChoice(choice: JourneyChoice) {
      if (!this.isChoiceAvailable(choice)) {
        return;
      }

      this.$emit('choice-selected', choice);
    },

    skipDecision() {
      this.$emit('choice-skipped');
    },

    toggleHelp() {
      this.showHelp = !this.showHelp;
    },

    isChoiceAvailable(choice: JourneyChoice): boolean {
      if (!choice.condition) {
        return true;
      }

      return this.evaluateCondition(choice.condition);
    },

    evaluateCondition(condition: JourneyCondition): boolean {
      // TODO: Implement proper condition evaluation
      // This would check system state, user properties, etc.
      
      switch (condition.type) {
        case 'system-state':
          return this.evaluateSystemStateCondition(condition);
        case 'user-property':
          return this.evaluateUserPropertyCondition(condition);
        case 'route':
          return this.evaluateRouteCondition(condition);
        case 'custom':
          return condition.customCheck ? condition.customCheck({
            journey: null, // TODO: Pass actual context
            progress: null,
            user: this.$store.getters['auth/user'],
            route: this.$route,
            store: this.$store,
          }) : true;
        default:
          return true;
      }
    },

    evaluateSystemStateCondition(condition: JourneyCondition): boolean {
      const systemState = this.$store.getters['journeys/getSystemState'];
      const value = systemState[condition.property];

      switch (condition.operator) {
        case 'equals':
          return value === condition.value;
        case 'not-equals':
          return value !== condition.value;
        case 'exists':
          return value !== undefined && value !== null;
        case 'not-exists':
          return value === undefined || value === null;
        case 'contains':
          return Array.isArray(value) ? value.includes(condition.value) : 
                 typeof value === 'string' ? value.includes(condition.value) : false;
        case 'greater-than':
          return typeof value === 'number' && value > condition.value;
        case 'less-than':
          return typeof value === 'number' && value < condition.value;
        default:
          return true;
      }
    },

    evaluateUserPropertyCondition(condition: JourneyCondition): boolean {
      const user = this.$store.getters['auth/user'];
      if (!user) return false;

      const value = user[condition.property];

      switch (condition.operator) {
        case 'equals':
          return value === condition.value;
        case 'not-equals':
          return value !== condition.value;
        case 'exists':
          return value !== undefined && value !== null;
        case 'not-exists':
          return value === undefined || value === null;
        default:
          return true;
      }
    },

    evaluateRouteCondition(condition: JourneyCondition): boolean {
      const route = this.$route;
      if (!route) return false;

      const value = route[condition.property] || route.query[condition.property] || route.params[condition.property];

      switch (condition.operator) {
        case 'equals':
          return value === condition.value;
        case 'not-equals':
          return value !== condition.value;
        case 'contains':
          return typeof value === 'string' && value.includes(condition.value);
        default:
          return true;
      }
    },
  },
});
</script>

<style lang="scss" scoped>
.decision-tree {
  max-width: 800px;
  margin: 0 auto;

  .decision-header {
    text-align: center;
    margin-bottom: 30px;

    .decision-title {
      margin-bottom: 15px;
      color: var(--primary-text);
      font-size: 1.5rem;
    }

    .decision-question {
      font-size: 1.1rem;
      color: var(--body-text);
      margin-bottom: 10px;
      font-weight: 500;
    }

    .decision-description {
      color: var(--body-text);
      line-height: 1.6;
    }
  }

  .decision-choices {
    display: grid;
    gap: 15px;
    margin-bottom: 30px;

    .choice-card {
      border: 2px solid var(--border);
      border-radius: 8px;
      padding: 20px;
      cursor: pointer;
      transition: all 0.2s ease;
      background: var(--body-bg);

      &:hover:not(.choice-disabled) {
        border-color: var(--primary);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transform: translateY(-1px);
      }

      &.choice-selected {
        border-color: var(--success);
        background: var(--success-bg);
      }

      &.choice-disabled {
        opacity: 0.6;
        cursor: not-allowed;
        background: var(--disabled-bg);
      }

      .choice-content {
        display: flex;
        align-items: flex-start;
        gap: 15px;

        .choice-icon {
          flex-shrink: 0;
          color: var(--primary);
        }

        .choice-text {
          flex: 1;

          .choice-label {
            margin: 0 0 8px 0;
            color: var(--primary-text);
            font-size: 1.1rem;
            font-weight: 600;
          }

          .choice-description {
            margin: 0;
            color: var(--body-text);
            line-height: 1.5;
            font-size: 0.95rem;
          }
        }

        .choice-indicator {
          flex-shrink: 0;
          display: flex;
          align-items: center;
          font-size: 1.2rem;
        }
      }

      .choice-details {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid var(--border);

        .choice-details-content {
          color: var(--body-text);
          font-size: 0.9rem;
          line-height: 1.5;
        }
      }
    }
  }

  .decision-content {
    margin-bottom: 20px;
    padding: 20px;
    background: var(--box-bg);
    border-radius: 6px;
    border: 1px solid var(--border);
  }

  .decision-help {
    margin-bottom: 20px;

    .help-content {
      h5 {
        margin: 0 0 10px 0;
        color: var(--primary-text);
      }

      p {
        margin: 0 0 10px 0;
        color: var(--body-text);
      }

      ul {
        margin: 0;
        padding-left: 20px;

        li {
          margin-bottom: 5px;
          color: var(--body-text);
        }
      }
    }
  }

  .decision-actions {
    display: flex;
    justify-content: center;
    gap: 10px;
    padding-top: 20px;
    border-top: 1px solid var(--border);
  }
}

// Responsive design
@media (max-width: 768px) {
  .decision-tree {
    .decision-choices {
      .choice-card {
        .choice-content {
          flex-direction: column;
          text-align: center;
          gap: 10px;

          .choice-icon {
            align-self: center;
          }

          .choice-indicator {
            align-self: center;
          }
        }
      }
    }
  }
}
</style>
