From 0000000000000000000000000000000000000000 Mon Sep 17 00:00:00 2001
From: Rancher <PERSON> <<EMAIL>>
Date: Tue, 13 Aug 2025 00:00:00 +0000
Subject: [PATCH] Fix EKS provider logo console error

Add EKS provider logo override to resolve console error:
"Can not find provider logo for provider eks"

The system was looking for 'eks.svg' but the actual logo file
is named 'amazoneks.svg'. This patch adds the mapping to the
PROVIDER_LOGO_OVERRIDE object and fixes the logo loading logic.

---
 shell/models/management.cattle.io.cluster.js | 5 +++--
 1 file changed, 3 insertions(+), 2 deletions(-)

diff --git a/shell/models/management.cattle.io.cluster.js b/shell/models/management.cattle.io.cluster.js
index 1234567..abcdefg 100644
--- a/shell/models/management.cattle.io.cluster.js
+++ b/shell/models/management.cattle.io.cluster.js
@@ -21,7 +21,8 @@ const DEFAULT_BADGE_COLOR = '#707070';
 
 // See translation file cluster.providers for list of providers
 // If the logo is not named with the provider name, add an override here
-const PROVIDER_LOGO_OVERRIDE = {};
+const PROVIDER_LOGO_OVERRIDE = {
+  eks: 'amazoneks'
+};
 
 function findRelationship(verb, type, relationships = []) {
   const from = `${ verb }Type`;
@@ -260,7 +261,7 @@ export default class MgmtCluster extends SteveModel {
     let icon;
 
     try {
-      icon = require(`~shell/assets/images/providers/${ prv }.svg`);
+      icon = require(`~shell/assets/images/providers/${ logo }.svg`);
     } catch (e) {
       console.warn(`Can not find provider logo for provider ${ prv }`); // eslint-disable-line no-console
       // Use fallback generic Kubernetes icon
-- 
2.39.0
