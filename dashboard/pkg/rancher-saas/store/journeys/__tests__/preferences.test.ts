/**
 * Journey Progress Persistence Tests
 * 
 * Tests for the journey progress persistence functionality using <PERSON>er's user preferences API.
 */

import { JOURNEY_PREFERENCES } from '../actions';
import type { JourneyProgress } from '../../../types/journey';

// Mock store for testing
const createMockStore = () => {
  const mockData: Record<string, any> = {};
  
  return {
    dispatch: jest.fn((action: string, payload: any) => {
      if (action === 'prefs/set') {
        mockData[payload.key] = payload.value;
        return Promise.resolve();
      }
      return Promise.resolve();
    }),
    getters: {
      'prefs/get': jest.fn((key: string) => mockData[key] || null),
    },
  };
};

describe('Journey Progress Persistence', () => {
  let mockStore: any;
  let actions: any;

  beforeEach(() => {
    mockStore = createMockStore();
    // Import actions dynamically to avoid module loading issues
    actions = require('../actions').default;
    jest.clearAllMocks();
  });

  describe('saveToUserPreferences', () => {
    it('should save journey progress to user preferences', async () => {
      const progress: JourneyProgress = {
        journeyId: 'test-journey',
        status: 'in-progress',
        currentStepId: 'step-1',
        currentStepIndex: 0,
        completedSteps: [],
        decisions: {},
        startedAt: new Date(),
        lastActiveAt: new Date(),
      };

      await actions.saveToUserPreferences(
        { dispatch: mockStore.dispatch, rootGetters: mockStore.getters },
        progress
      );

      expect(mockStore.dispatch).toHaveBeenCalledWith(
        'prefs/set',
        {
          key: JOURNEY_PREFERENCES.PROGRESS,
          value: expect.objectContaining({
            'test-journey': expect.objectContaining({
              journeyId: 'test-journey',
              status: 'in-progress',
              currentStepId: 'step-1',
            }),
          }),
        },
        { root: true }
      );
    });

    it('should merge with existing progress data', async () => {
      // Set up existing progress
      const existingProgress = {
        'existing-journey': {
          journeyId: 'existing-journey',
          status: 'completed',
          completedSteps: ['step-1', 'step-2'],
        },
      };
      mockStore.getters['prefs/get'].mockReturnValue(existingProgress);

      const newProgress: JourneyProgress = {
        journeyId: 'new-journey',
        status: 'in-progress',
        currentStepId: 'step-1',
        currentStepIndex: 0,
        completedSteps: [],
        decisions: {},
        startedAt: new Date(),
        lastActiveAt: new Date(),
      };

      await actions.saveToUserPreferences(
        { dispatch: mockStore.dispatch, rootGetters: mockStore.getters },
        newProgress
      );

      expect(mockStore.dispatch).toHaveBeenCalledWith(
        'prefs/set',
        {
          key: JOURNEY_PREFERENCES.PROGRESS,
          value: expect.objectContaining({
            'existing-journey': existingProgress['existing-journey'],
            'new-journey': expect.objectContaining({
              journeyId: 'new-journey',
              status: 'in-progress',
            }),
          }),
        },
        { root: true }
      );
    });
  });

  describe('loadFromUserPreferences', () => {
    it('should load journey progress from user preferences', async () => {
      const mockProgress = {
        'test-journey': {
          journeyId: 'test-journey',
          status: 'in-progress',
          currentStepId: 'step-2',
        },
      };
      mockStore.getters['prefs/get'].mockReturnValue(mockProgress);

      const result = await actions.loadFromUserPreferences({
        rootGetters: mockStore.getters,
      });

      expect(mockStore.getters['prefs/get']).toHaveBeenCalledWith(JOURNEY_PREFERENCES.PROGRESS);
      expect(result).toEqual(mockProgress);
    });

    it('should return empty object when no progress exists', async () => {
      mockStore.getters['prefs/get'].mockReturnValue(null);

      const result = await actions.loadFromUserPreferences({
        rootGetters: mockStore.getters,
      });

      expect(result).toEqual({});
    });

    it('should handle errors gracefully', async () => {
      mockStore.getters['prefs/get'].mockImplementation(() => {
        throw new Error('Preferences error');
      });

      const result = await actions.loadFromUserPreferences({
        rootGetters: mockStore.getters,
      });

      expect(result).toEqual({});
    });
  });

  describe('saveJourneyConfig', () => {
    it('should save journey configuration to user preferences', async () => {
      const config = {
        enableJourneys: false,
        debugMode: true,
      };

      await actions.saveJourneyConfig(
        { dispatch: mockStore.dispatch, rootGetters: mockStore.getters },
        config
      );

      expect(mockStore.dispatch).toHaveBeenCalledWith(
        'prefs/set',
        {
          key: JOURNEY_PREFERENCES.CONFIG,
          value: expect.objectContaining(config),
        },
        { root: true }
      );
    });
  });

  describe('saveJourneyAnalytics', () => {
    it('should save journey analytics to user preferences', async () => {
      const analytics = {
        journeyStartCount: { 'test-journey': 1 },
        journeyCompletionCount: { 'test-journey': 1 },
      };

      await actions.saveJourneyAnalytics(
        { dispatch: mockStore.dispatch, rootGetters: mockStore.getters },
        analytics
      );

      expect(mockStore.dispatch).toHaveBeenCalledWith(
        'prefs/set',
        {
          key: JOURNEY_PREFERENCES.ANALYTICS,
          value: expect.objectContaining(analytics),
        },
        { root: true }
      );
    });
  });

  describe('initializeFromPreferences', () => {
    it('should initialize journey system from saved preferences', async () => {
      const mockCommit = jest.fn();
      const mockDispatch = jest.fn().mockResolvedValue({});

      const result = await actions.initializeFromPreferences({
        dispatch: mockDispatch,
        commit: mockCommit,
      });

      expect(mockDispatch).toHaveBeenCalledWith('loadFromUserPreferences');
      expect(mockDispatch).toHaveBeenCalledWith('loadJourneyConfig');
      expect(mockDispatch).toHaveBeenCalledWith('loadJourneyAnalytics');
      expect(mockDispatch).toHaveBeenCalledWith('loadSystemState');
      expect(mockDispatch).toHaveBeenCalledWith('loadUIState');
      expect(mockDispatch).toHaveBeenCalledWith('loadCompletedJourneys');

      expect(result).toBeDefined();
    });
  });

  describe('clearAllJourneyData', () => {
    it('should clear all journey data from user preferences', async () => {
      await actions.clearAllJourneyData({
        dispatch: mockStore.dispatch,
      });

      // Should dispatch prefs/set for each preference type
      expect(mockStore.dispatch).toHaveBeenCalledTimes(6);
      expect(mockStore.dispatch).toHaveBeenCalledWith(
        'prefs/set',
        { key: JOURNEY_PREFERENCES.PROGRESS, value: {} },
        { root: true }
      );
    });
  });
});
