import Vue from 'vue';
import type { 
  JourneyState, 
  JourneyDefinition, 
  JourneyProgress, 
  JourneyAction,
  JourneyEventPayload 
} from '../../types/journey';

export default {
  // Journey Definition Management
  SET_JOURNEY_DEFINITIONS(state: JourneyState, definitions: Record<string, JourneyDefinition>) {
    state.definitions = { ...definitions };
  },

  ADD_JOURNEY_DEFINITION(state: JourneyState, definition: JourneyDefinition) {
    Vue.set(state.definitions, definition.id, definition);
  },

  REMOVE_JOURNEY_DEFINITION(state: JourneyState, journeyId: string) {
    Vue.delete(state.definitions, journeyId);
  },

  // Current Journey Management
  SET_CURRENT_JOURNEY(state: JourneyState, journey: JourneyDefinition | null) {
    state.currentJourney = journey;
  },

  SET_CURRENT_PROGRESS(state: JourneyState, progress: JourneyProgress | null) {
    state.currentProgress = progress;
  },

  // User Progress Management
  SET_USER_PROGRESS(state: JourneyState, { journeyId, progress }: { journeyId: string, progress: JourneyProgress }) {
    Vue.set(state.userProgress, journeyId, progress);
  },

  UPDATE_USER_PROGRESS(state: JourneyState, { journeyId, updates }: { journeyId: string, updates: Partial<JourneyProgress> }) {
    const existing = state.userProgress[journeyId];
    if (existing) {
      Vue.set(state.userProgress, journeyId, { ...existing, ...updates });
    }
  },

  REMOVE_USER_PROGRESS(state: JourneyState, journeyId: string) {
    Vue.delete(state.userProgress, journeyId);
  },

  // Step Progress Management
  ADD_COMPLETED_STEP(state: JourneyState, { journeyId, stepId }: { journeyId: string, stepId: string }) {
    const progress = state.userProgress[journeyId];
    if (progress && !progress.completedSteps.includes(stepId)) {
      progress.completedSteps.push(stepId);
      progress.lastActiveAt = new Date();
    }
  },

  SET_CURRENT_STEP(state: JourneyState, { journeyId, stepId, stepIndex }: { journeyId: string, stepId: string, stepIndex: number }) {
    const progress = state.userProgress[journeyId];
    if (progress) {
      progress.currentStepId = stepId;
      progress.currentStepIndex = stepIndex;
      progress.lastActiveAt = new Date();
    }
  },

  // Decision Management
  ADD_DECISION(state: JourneyState, { journeyId, stepId, choiceId, data }: { journeyId: string, stepId: string, choiceId: string, data?: any }) {
    const progress = state.userProgress[journeyId];
    if (progress) {
      Vue.set(progress.decisions, stepId, { choiceId, data, timestamp: new Date() });
    }
    
    // Also update global decision points for analytics
    const key = `${journeyId}:${stepId}`;
    Vue.set(state.decisionPoints, key, { choiceId, data, timestamp: new Date() });
  },

  // Journey History Management
  ADD_JOURNEY_HISTORY(state: JourneyState, progress: JourneyProgress) {
    state.journeyHistory.unshift(progress);
    
    // Keep only last 50 entries
    if (state.journeyHistory.length > 50) {
      state.journeyHistory = state.journeyHistory.slice(0, 50);
    }
  },

  // System State Management
  SET_SYSTEM_STATE(state: JourneyState, systemState: Record<string, any>) {
    state.systemState = { ...state.systemState, ...systemState };
    
    // Update cache hash
    state.cache.systemStateHash = JSON.stringify(systemState);
    state.cache.lastCacheUpdate = new Date();
  },

  UPDATE_SYSTEM_STATE(state: JourneyState, updates: Record<string, any>) {
    Object.keys(updates).forEach(key => {
      Vue.set(state.systemState, key, updates[key]);
    });
    
    // Update cache hash
    state.cache.systemStateHash = JSON.stringify(state.systemState);
    state.cache.lastCacheUpdate = new Date();
  },

  // Pending Actions Management
  ADD_PENDING_ACTION(state: JourneyState, action: JourneyAction) {
    state.pendingActions.push(action);
  },

  REMOVE_PENDING_ACTION(state: JourneyState, actionIndex: number) {
    state.pendingActions.splice(actionIndex, 1);
  },

  CLEAR_PENDING_ACTIONS(state: JourneyState) {
    state.pendingActions = [];
  },

  // Journey System State
  SET_JOURNEY_ACTIVE(state: JourneyState, isActive: boolean) {
    state.isActive = isActive;
  },

  SET_JOURNEY_LOADING(state: JourneyState, isLoading: boolean) {
    state.isLoading = isLoading;
  },

  // Configuration Management
  SET_JOURNEY_CONFIG(state: JourneyState, config: Partial<JourneyState['config']>) {
    state.config = { ...state.config, ...config };
  },

  // Error Management
  ADD_ERROR(state: JourneyState, error: Error | string) {
    const errorObj = typeof error === 'string' ? new Error(error) : error;
    state.errors.push({
      message: errorObj.message,
      stack: errorObj.stack,
      timestamp: new Date(),
    });
    state.lastError = errorObj;
    
    // Keep only last 10 errors
    if (state.errors.length > 10) {
      state.errors = state.errors.slice(-10);
    }
  },

  CLEAR_ERRORS(state: JourneyState) {
    state.errors = [];
    state.lastError = null;
  },

  // Analytics Management
  INCREMENT_JOURNEY_START_COUNT(state: JourneyState, journeyId: string) {
    const current = state.analytics.journeyStartCount[journeyId] || 0;
    Vue.set(state.analytics.journeyStartCount, journeyId, current + 1);
  },

  INCREMENT_JOURNEY_COMPLETION_COUNT(state: JourneyState, journeyId: string) {
    const current = state.analytics.journeyCompletionCount[journeyId] || 0;
    Vue.set(state.analytics.journeyCompletionCount, journeyId, current + 1);
  },

  INCREMENT_JOURNEY_SKIP_COUNT(state: JourneyState, journeyId: string) {
    const current = state.analytics.journeySkipCount[journeyId] || 0;
    Vue.set(state.analytics.journeySkipCount, journeyId, current + 1);
  },

  UPDATE_AVERAGE_COMPLETION_TIME(state: JourneyState, { journeyId, duration }: { journeyId: string, duration: number }) {
    const current = state.analytics.averageCompletionTime[journeyId] || 0;
    const count = state.analytics.journeyCompletionCount[journeyId] || 1;
    const newAverage = (current * (count - 1) + duration) / count;
    Vue.set(state.analytics.averageCompletionTime, journeyId, newAverage);
  },

  ADD_DROP_OFF_POINT(state: JourneyState, { journeyId, stepId }: { journeyId: string, stepId: string }) {
    const key = `${journeyId}:${stepId}`;
    const current = state.analytics.dropOffPoints[key] || 0;
    Vue.set(state.analytics.dropOffPoints, key, current + 1);
  },

  RECORD_CHOICE_SELECTION(state: JourneyState, { journeyId, stepId, choiceId }: { journeyId: string, stepId: string, choiceId: string }) {
    const key = `${journeyId}:${stepId}:${choiceId}`;
    const current = state.analytics.popularChoices[key] || 0;
    Vue.set(state.analytics.popularChoices, key, current + 1);
  },

  ADD_USER_PATH(state: JourneyState, path: { journeyId: string, steps: string[], decisions: Record<string, any>, duration: number }) {
    state.analytics.userPaths.push({
      ...path,
      timestamp: new Date(),
    });
    
    // Keep only last 100 paths
    if (state.analytics.userPaths.length > 100) {
      state.analytics.userPaths = state.analytics.userPaths.slice(-100);
    }
  },

  // Cache Management
  SET_AVAILABLE_JOURNEYS_CACHE(state: JourneyState, journeys: JourneyDefinition[]) {
    state.cache.availableJourneys = journeys;
    state.cache.lastCacheUpdate = new Date();
  },

  CLEAR_CACHE(state: JourneyState) {
    state.cache.availableJourneys = null;
    state.cache.systemStateHash = null;
    state.cache.lastCacheUpdate = null;
  },

  // Trigger Management
  SET_NAVIGATION_TRIGGERS(state: JourneyState, triggers: Record<string, any>) {
    state.triggers.navigationTriggers = triggers;
  },

  SET_EVENT_TRIGGERS(state: JourneyState, triggers: Record<string, any>) {
    state.triggers.eventTriggers = triggers;
  },

  // UI State Management
  SET_JOURNEY_OVERLAY_VISIBLE(state: JourneyState, visible: boolean) {
    state.ui.showJourneyOverlay = visible;
  },

  SET_JOURNEY_OVERLAY_POSITION(state: JourneyState, position: string) {
    state.ui.overlayPosition = position;
  },

  SET_JOURNEY_MINIMIZED(state: JourneyState, minimized: boolean) {
    state.ui.minimized = minimized;
  },

  SET_TOUR_MODE(state: JourneyState, tourMode: boolean) {
    state.ui.tourMode = tourMode;
  },

  UPDATE_LAST_INTERACTION(state: JourneyState) {
    state.ui.lastInteraction = new Date();
  },
};
