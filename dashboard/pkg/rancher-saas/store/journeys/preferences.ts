/**
 * Journey User Preferences Integration
 * 
 * This module integrates with <PERSON><PERSON>'s user preferences system to persist
 * journey progress, configuration, and analytics data across sessions.
 */

import { create } from '@shell/store/prefs';
import type { JourneyProgress, JourneyState } from '../../types/journey';

// Define preference schemas following <PERSON><PERSON>'s patterns
export const JOURNEY_PREFERENCES = {
  // Journey progress tracking
  PROGRESS: create('journey-progress', {}, {
    parseJSON: true,
    asUserPreference: true,
    mangleNames: false,
  }),

  // Journey configuration settings
  CONFIG: create('journey-config', {
    enableJourneys: true,
    enableAnalytics: true,
    debugMode: false,
    autoTrigger: true,
    tourMode: false,
  }, {
    parseJSON: true,
    asUserPreference: true,
    mangleNames: false,
  }),

  // Journey analytics data
  ANALYTICS: create('journey-analytics', {
    journeyStartCount: {},
    journeyCompletionCount: {},
    journeySkipCount: {},
    averageCompletionTime: {},
    dropOffPoints: {},
    popularChoices: {},
    userPaths: [],
  }, {
    parseJSON: true,
    asUserPreference: true,
    mangleNames: false,
  }),

  // System state for journey triggering
  SYSTEM_STATE: create('journey-system-state', {
    lastLogin: null,
    isFirstLogin: false,
    hasCompletedOnboarding: false,
    clusterCount: 0,
    credentialCount: 0,
    applicationCount: 0,
  }, {
    parseJSON: true,
    asUserPreference: true,
    mangleNames: false,
  }),

  // Completed journeys list
  COMPLETED_JOURNEYS: create('journey-completed', [], {
    parseJSON: true,
    asUserPreference: true,
    mangleNames: false,
  }),

  // UI state preferences
  UI_STATE: create('journey-ui-state', {
    showJourneyOverlay: false,
    overlayPosition: 'center',
    minimized: false,
    lastInteraction: null,
  }, {
    parseJSON: true,
    asUserPreference: true,
    mangleNames: false,
  }),
} as const;

export interface PreferencesManager {
  saveProgress(progress: JourneyProgress): Promise<void>;
  loadProgress(journeyId?: string): Promise<Record<string, JourneyProgress> | JourneyProgress | null>;
  saveConfig(config: Partial<JourneyState['config']>): Promise<void>;
  loadConfig(): Promise<Partial<JourneyState['config']> | null>;
  saveAnalytics(analytics: Partial<JourneyState['analytics']>): Promise<void>;
  loadAnalytics(): Promise<Partial<JourneyState['analytics']> | null>;
  saveSystemState(systemState: Record<string, any>): Promise<void>;
  loadSystemState(): Promise<Record<string, any> | null>;
  saveUIState(uiState: Partial<JourneyState['ui']>): Promise<void>;
  loadUIState(): Promise<Partial<JourneyState['ui']> | null>;
  clearAllData(): Promise<void>;
  exportData(): Promise<any>;
  importData(data: any): Promise<void>;
}

export class RancherPreferencesManager implements PreferencesManager {
  private store: any;

  constructor(store: any) {
    this.store = store;
  }

  /**
   * Save journey progress to user preferences
   */
  async saveProgress(progress: JourneyProgress): Promise<void> {
    try {
      const currentProgress = await this.loadProgress() as Record<string, JourneyProgress> || {};
      currentProgress[progress.journeyId] = {
        ...progress,
        lastActiveAt: new Date(),
      };

      await this.store.dispatch('prefs/set', {
        key: JOURNEY_PREFERENCES.PROGRESS,
        value: currentProgress,
      });

      console.log(`Journey progress saved for: ${progress.journeyId}`);
    } catch (error) {
      console.error('Failed to save journey progress:', error);
      throw error;
    }
  }

  /**
   * Load journey progress from user preferences
   */
  async loadProgress(journeyId?: string): Promise<Record<string, JourneyProgress> | JourneyProgress | null> {
    try {
      const progress = this.store.getters['prefs/get'](JOURNEY_PREFERENCES.PROGRESS);
      
      if (!progress) return null;

      if (journeyId) {
        return progress[journeyId] || null;
      }

      return progress;
    } catch (error) {
      console.error('Failed to load journey progress:', error);
      return null;
    }
  }

  /**
   * Save journey configuration
   */
  async saveConfig(config: Partial<JourneyState['config']>): Promise<void> {
    try {
      const currentConfig = await this.loadConfig() || {};
      const updatedConfig = { ...currentConfig, ...config };

      await this.store.dispatch('prefs/set', {
        key: JOURNEY_PREFERENCES.CONFIG,
        value: updatedConfig,
      });

      console.log('Journey config saved');
    } catch (error) {
      console.error('Failed to save journey config:', error);
      throw error;
    }
  }

  /**
   * Load journey configuration
   */
  async loadConfig(): Promise<Partial<JourneyState['config']> | null> {
    try {
      return this.store.getters['prefs/get'](JOURNEY_PREFERENCES.CONFIG);
    } catch (error) {
      console.error('Failed to load journey config:', error);
      return null;
    }
  }

  /**
   * Save journey analytics data
   */
  async saveAnalytics(analytics: Partial<JourneyState['analytics']>): Promise<void> {
    try {
      const currentAnalytics = await this.loadAnalytics() || {};
      const updatedAnalytics = this.mergeAnalytics(currentAnalytics, analytics);

      await this.store.dispatch('prefs/set', {
        key: JOURNEY_PREFERENCES.ANALYTICS,
        value: updatedAnalytics,
      });

      console.log('Journey analytics saved');
    } catch (error) {
      console.error('Failed to save journey analytics:', error);
      throw error;
    }
  }

  /**
   * Load journey analytics data
   */
  async loadAnalytics(): Promise<Partial<JourneyState['analytics']> | null> {
    try {
      return this.store.getters['prefs/get'](JOURNEY_PREFERENCES.ANALYTICS);
    } catch (error) {
      console.error('Failed to load journey analytics:', error);
      return null;
    }
  }

  /**
   * Save system state for journey triggering
   */
  async saveSystemState(systemState: Record<string, any>): Promise<void> {
    try {
      const currentState = await this.loadSystemState() || {};
      const updatedState = { ...currentState, ...systemState };

      await this.store.dispatch('prefs/set', {
        key: JOURNEY_PREFERENCES.SYSTEM_STATE,
        value: updatedState,
      });

      console.log('Journey system state saved');
    } catch (error) {
      console.error('Failed to save journey system state:', error);
      throw error;
    }
  }

  /**
   * Load system state
   */
  async loadSystemState(): Promise<Record<string, any> | null> {
    try {
      return this.store.getters['prefs/get'](JOURNEY_PREFERENCES.SYSTEM_STATE);
    } catch (error) {
      console.error('Failed to load journey system state:', error);
      return null;
    }
  }

  /**
   * Save UI state preferences
   */
  async saveUIState(uiState: Partial<JourneyState['ui']>): Promise<void> {
    try {
      const currentUIState = await this.loadUIState() || {};
      const updatedUIState = { ...currentUIState, ...uiState };

      await this.store.dispatch('prefs/set', {
        key: JOURNEY_PREFERENCES.UI_STATE,
        value: updatedUIState,
      });

      console.log('Journey UI state saved');
    } catch (error) {
      console.error('Failed to save journey UI state:', error);
      throw error;
    }
  }

  /**
   * Load UI state preferences
   */
  async loadUIState(): Promise<Partial<JourneyState['ui']> | null> {
    try {
      return this.store.getters['prefs/get'](JOURNEY_PREFERENCES.UI_STATE);
    } catch (error) {
      console.error('Failed to load journey UI state:', error);
      return null;
    }
  }

  /**
   * Clear all journey data from preferences
   */
  async clearAllData(): Promise<void> {
    try {
      const keys = Object.values(JOURNEY_PREFERENCES);
      
      for (const key of keys) {
        await this.store.dispatch('prefs/unset', { key });
      }

      console.log('All journey data cleared from preferences');
    } catch (error) {
      console.error('Failed to clear journey data:', error);
      throw error;
    }
  }

  /**
   * Export all journey data
   */
  async exportData(): Promise<any> {
    try {
      const data: any = {};

      for (const [name, pref] of Object.entries(JOURNEY_PREFERENCES)) {
        data[name] = this.store.getters['prefs/get'](pref);
      }

      return {
        ...data,
        exportedAt: new Date().toISOString(),
        version: '1.0.0',
      };
    } catch (error) {
      console.error('Failed to export journey data:', error);
      throw error;
    }
  }

  /**
   * Import journey data
   */
  async importData(data: any): Promise<void> {
    try {
      if (!data || typeof data !== 'object') {
        throw new Error('Invalid import data');
      }

      for (const [name, pref] of Object.entries(JOURNEY_PREFERENCES)) {
        if (data[name]) {
          await this.store.dispatch('prefs/set', {
            key: pref,
            value: data[name],
          });
        }
      }

      console.log('Journey data imported successfully');
    } catch (error) {
      console.error('Failed to import journey data:', error);
      throw error;
    }
  }

  /**
   * Merge analytics data intelligently
   */
  private mergeAnalytics(current: any, updates: any): any {
    const merged = { ...current };

    // Merge count objects by adding values
    ['journeyStartCount', 'journeyCompletionCount', 'journeySkipCount', 'dropOffPoints', 'popularChoices'].forEach(key => {
      if (updates[key]) {
        merged[key] = merged[key] || {};
        Object.keys(updates[key]).forEach(subKey => {
          merged[key][subKey] = (merged[key][subKey] || 0) + updates[key][subKey];
        });
      }
    });

    // Merge average completion times
    if (updates.averageCompletionTime) {
      merged.averageCompletionTime = merged.averageCompletionTime || {};
      Object.keys(updates.averageCompletionTime).forEach(journeyId => {
        const currentAvg = merged.averageCompletionTime[journeyId] || 0;
        const newTime = updates.averageCompletionTime[journeyId];
        const currentCount = merged.journeyCompletionCount?.[journeyId] || 1;
        
        // Calculate weighted average
        merged.averageCompletionTime[journeyId] = 
          (currentAvg * (currentCount - 1) + newTime) / currentCount;
      });
    }

    // Append user paths (keep last 100)
    if (updates.userPaths && Array.isArray(updates.userPaths)) {
      merged.userPaths = merged.userPaths || [];
      merged.userPaths.push(...updates.userPaths);
      
      if (merged.userPaths.length > 100) {
        merged.userPaths = merged.userPaths.slice(-100);
      }
    }

    return merged;
  }
}

/**
 * Create preferences manager instance
 */
export function createPreferencesManager(store: any): PreferencesManager {
  return new RancherPreferencesManager(store);
}
