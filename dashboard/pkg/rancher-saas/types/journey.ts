/**
 * PLG Journey System Type Definitions
 * 
 * This file defines the TypeScript interfaces and types for the PLG Journey System
 * that extends <PERSON><PERSON>'s existing Wizard component for guided user workflows.
 */

export interface JourneyChoice {
  id: string;
  label: string;
  description?: string;
  icon?: string;
  next?: string; // Next step ID
  action?: string; // Action to execute
  condition?: JourneyCondition;
}

export interface JourneyCondition {
  type: 'system-state' | 'user-property' | 'route' | 'custom';
  property: string;
  operator: 'equals' | 'not-equals' | 'exists' | 'not-exists' | 'contains' | 'greater-than' | 'less-than';
  value?: any;
  customCheck?: (context: JourneyContext) => boolean;
}

export interface JourneyAction {
  type: 'navigate' | 'create-resource' | 'update-resource' | 'trigger-workflow' | 'show-modal' | 'custom';
  target?: string; // Route name or resource type
  params?: Record<string, any>;
  payload?: Record<string, any>;
  customHandler?: (context: JourneyContext) => Promise<any>;
}

export interface JourneyStep {
  id: string;
  name: string;
  label: string;
  description?: string;
  type: 'welcome' | 'education' | 'decision' | 'workflow' | 'celebration' | 'custom';
  content?: string;
  component?: string; // Custom component name
  
  // Decision step properties
  question?: string;
  choices?: JourneyChoice[];
  
  // Workflow step properties
  action?: JourneyAction;
  
  // Step behavior
  skippable?: boolean;
  required?: boolean;
  condition?: JourneyCondition;
  
  // Wizard integration
  ready?: boolean;
  loading?: boolean;
  hidden?: boolean;
  subtext?: string;
  nextButton?: {
    labelKey?: string;
    style?: string;
  };
  previousButton?: {
    disable?: boolean;
  };
}

export interface JourneyTrigger {
  event: 'first-login' | 'navigation' | 'resource-created' | 'resource-updated' | 'custom';
  conditions?: JourneyCondition[];
  route?: string;
  resource?: string;
  priority?: number; // Higher priority triggers first
}

export interface JourneyDefinition {
  id: string;
  name: string;
  description: string;
  version: string;
  category: 'onboarding' | 'feature-discovery' | 'workflow-completion' | 'education';
  
  // Journey behavior
  steps: JourneyStep[];
  triggers: JourneyTrigger[];
  
  // Journey metadata
  estimatedDuration?: number; // in minutes
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  tags?: string[];
  
  // Journey configuration
  allowSkip?: boolean;
  allowRestart?: boolean;
  persistProgress?: boolean;
  showProgress?: boolean;
  
  // Wizard integration
  bannerTitle?: string;
  bannerTitleSubtext?: string;
  bannerImage?: string;
  
  // Lifecycle hooks
  onStart?: (context: JourneyContext) => Promise<void>;
  onComplete?: (context: JourneyContext) => Promise<void>;
  onSkip?: (context: JourneyContext) => Promise<void>;
  onError?: (error: Error, context: JourneyContext) => Promise<void>;
}

export interface JourneyProgress {
  journeyId: string;
  userId?: string;
  status: 'not-started' | 'in-progress' | 'completed' | 'skipped' | 'error';
  currentStepId?: string;
  currentStepIndex?: number;
  completedSteps: string[];
  decisions: Record<string, any>; // Decision choices made
  startedAt?: Date;
  completedAt?: Date;
  lastActiveAt?: Date;
  metadata?: Record<string, any>;
}

export interface JourneyContext {
  journey: JourneyDefinition;
  progress: JourneyProgress;
  user?: any; // User object from auth store
  route?: any; // Current route
  store?: any; // Vuex store
  systemState?: Record<string, any>; // Current system state
  [key: string]: any; // Additional context data
}

export interface JourneyState {
  // Journey definitions
  definitions: Record<string, JourneyDefinition>;
  
  // User progress tracking
  userProgress: Record<string, JourneyProgress>;
  
  // Current active journey
  currentJourney: JourneyDefinition | null;
  currentProgress: JourneyProgress | null;
  
  // Journey history
  journeyHistory: JourneyProgress[];
  
  // System state for triggering
  systemState: Record<string, any>;
  
  // Journey system state
  isActive: boolean;
  isLoading: boolean;
  
  // Pending actions and decisions
  pendingActions: JourneyAction[];
  decisionPoints: Record<string, any>;
  
  // Configuration
  config: {
    enableJourneys: boolean;
    enableAnalytics: boolean;
    debugMode: boolean;
  };
}

export interface JourneyStoreActions {
  // Journey management
  loadJourneyDefinitions(): Promise<void>;
  startJourney(journeyId: string, context?: Partial<JourneyContext>): Promise<void>;
  resumeJourney(journeyId: string): Promise<void>;
  completeJourney(journeyId: string): Promise<void>;
  skipJourney(journeyId: string): Promise<void>;
  restartJourney(journeyId: string): Promise<void>;
  
  // Step navigation
  nextStep(): Promise<void>;
  previousStep(): Promise<void>;
  goToStep(stepId: string): Promise<void>;
  
  // Decision handling
  makeDecision(stepId: string, choiceId: string, data?: any): Promise<void>;
  
  // Workflow execution
  executeAction(action: JourneyAction, context: JourneyContext): Promise<any>;
  
  // Progress tracking
  updateProgress(journeyId: string, progress: Partial<JourneyProgress>): Promise<void>;
  saveProgress(): Promise<void>;
  loadProgress(): Promise<void>;
  
  // System state
  updateSystemState(state: Record<string, any>): void;
  checkTriggers(event: string, context?: any): Promise<void>;
  
  // Configuration
  setConfig(config: Partial<JourneyState['config']>): void;
}

export interface JourneyStoreGetters {
  // Current journey
  currentJourney: JourneyDefinition | null;
  currentStep: JourneyStep | null;
  currentProgress: JourneyProgress | null;
  
  // Journey queries
  getJourneyById: (id: string) => JourneyDefinition | null;
  getProgressById: (journeyId: string) => JourneyProgress | null;
  getAvailableJourneys: (context?: any) => JourneyDefinition[];
  
  // Step queries
  getCurrentStepIndex: number;
  getTotalSteps: number;
  getVisibleSteps: JourneyStep[];
  canGoNext: boolean;
  canGoPrevious: boolean;
  
  // Progress queries
  getCompletionPercentage: (journeyId?: string) => number;
  isJourneyCompleted: (journeyId: string) => boolean;
  isJourneyInProgress: (journeyId: string) => boolean;
  
  // System state
  getSystemState: Record<string, any>;
  shouldTriggerJourney: (event: string, context?: any) => JourneyDefinition | null;
  
  // Configuration
  isJourneysEnabled: boolean;
  isAnalyticsEnabled: boolean;
  isDebugMode: boolean;
}

// Event types for journey system
export type JourneyEvent = 
  | 'journey-started'
  | 'journey-completed'
  | 'journey-skipped'
  | 'journey-error'
  | 'step-entered'
  | 'step-completed'
  | 'decision-made'
  | 'action-executed'
  | 'progress-saved';

export interface JourneyEventPayload {
  event: JourneyEvent;
  journeyId: string;
  stepId?: string;
  data?: any;
  timestamp: Date;
  context?: JourneyContext;
}

// Utility types
export type JourneyStepType = JourneyStep['type'];
export type JourneyStatus = JourneyProgress['status'];
export type JourneyCategory = JourneyDefinition['category'];
export type JourneyTriggerEvent = JourneyTrigger['event'];
